jQuery(document).ready(function($) {
    $('#tcp-parser-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        const $results = $('#tcp-results');
        
        $button.prop('disabled', true);
        $results.html('<div class="notice notice-info"><p>Идет парсинг...</p></div>');
        
        $.post(tcp_ajax.url, {
            action: 'tcp_parse_channel',
            channel: $form.find('#tcp-channel').val(),
            limit: $form.find('#tcp-limit').val(),
            _ajax_nonce: tcp_ajax.nonce
        }).done(function(response) {
            $results.html('<div class="notice notice-success"><p>' + response.data + '</p></div>');
        }).fail(function(xhr) {
            let error = xhr.responseJSON?.data || 'Ошибка сервера';
            $results.html('<div class="notice notice-error"><p>Ошибка: ' + error + '</p></div>');
        }).always(function() {
            $button.prop('disabled', false);
        });
    });
});