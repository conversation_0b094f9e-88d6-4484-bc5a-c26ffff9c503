{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "a848b8596c51a9d52a38a9f1a2979760", "packages": [], "packages-dev": [{"name": "symfony/finder", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/ec2344cf77a48253bbca6939aa3d2477773ea63d", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:26+00:00"}, {"name": "wp-cli/mustache", "version": "v2.14.99", "source": {"type": "git", "url": "https://github.com/wp-cli/mustache.php.git", "reference": "ca23b97ac35fbe01c160549eb634396183d04a59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/mustache.php/zipball/ca23b97ac35fbe01c160549eb634396183d04a59", "reference": "ca23b97ac35fbe01c160549eb634396183d04a59", "shasum": ""}, "require": {"php": ">=5.6"}, "replace": {"mustache/mustache": "^2.14.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.19.3", "yoast/phpunit-polyfills": "^2.0"}, "type": "library", "autoload": {"psr-0": {"Mustache": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "A Mustache implementation in PHP.", "homepage": "https://github.com/bobthecow/mustache.php", "keywords": ["mustache", "templating"], "support": {"source": "https://github.com/wp-cli/mustache.php/tree/v2.14.99"}, "time": "2025-05-06T16:15:37+00:00"}, {"name": "wp-cli/mustangostang-spyc", "version": "0.6.3", "source": {"type": "git", "url": "https://github.com/wp-cli/spyc.git", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/spyc/zipball/6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "4.3.*@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.5.x-dev"}}, "autoload": {"files": ["includes/functions.php"], "psr-4": {"Mustangostang\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "mustangostang", "email": "<EMAIL>"}], "description": "A simple YAML loader/dumper class for PHP (WP-CLI fork)", "homepage": "https://github.com/mustangostang/spyc/", "support": {"source": "https://github.com/wp-cli/spyc/tree/autoload"}, "time": "2017-04-25T11:26:20+00:00"}, {"name": "wp-cli/php-cli-tools", "version": "v0.12.5", "source": {"type": "git", "url": "https://github.com/wp-cli/php-cli-tools.git", "reference": "34b83b4f700df8a4ec3fd17bf7e7e7d8ca5f28da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/php-cli-tools/zipball/34b83b4f700df8a4ec3fd17bf7e7e7d8ca5f28da", "reference": "34b83b4f700df8a4ec3fd17bf7e7e7d8ca5f28da", "shasum": ""}, "require": {"php": ">= 5.6.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "wp-cli/wp-cli-tests": "^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"files": ["lib/cli/cli.php"], "psr-0": {"cli": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Console utilities for PHP", "homepage": "http://github.com/wp-cli/php-cli-tools", "keywords": ["cli", "console"], "support": {"issues": "https://github.com/wp-cli/php-cli-tools/issues", "source": "https://github.com/wp-cli/php-cli-tools/tree/v0.12.5"}, "time": "2025-03-26T16:13:46+00:00"}, {"name": "wp-cli/wp-cli", "version": "v2.12.0", "source": {"type": "git", "url": "https://github.com/wp-cli/wp-cli.git", "reference": "03d30d4138d12b4bffd8b507b82e56e129e0523f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/wp-cli/zipball/03d30d4138d12b4bffd8b507b82e56e129e0523f", "reference": "03d30d4138d12b4bffd8b507b82e56e129e0523f", "shasum": ""}, "require": {"ext-curl": "*", "php": "^5.6 || ^7.0 || ^8.0", "symfony/finder": ">2.7", "wp-cli/mustache": "^2.14.99", "wp-cli/mustangostang-spyc": "^0.6.3", "wp-cli/php-cli-tools": "~0.12.4"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.2 || ^2", "wp-cli/extension-command": "^1.1 || ^2", "wp-cli/package-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^4.3.10"}, "suggest": {"ext-readline": "Include for a better --prompt implementation", "ext-zip": "Needed to support extraction of ZIP archives when doing downloads or updates"}, "bin": ["bin/wp", "bin/wp.bat"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.12.x-dev"}}, "autoload": {"psr-0": {"WP_CLI\\": "php/"}, "classmap": ["php/class-wp-cli.php", "php/class-wp-cli-command.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WP-CLI framework", "homepage": "https://wp-cli.org", "keywords": ["cli", "wordpress"], "support": {"docs": "https://make.wordpress.org/cli/handbook/", "issues": "https://github.com/wp-cli/wp-cli/issues", "source": "https://github.com/wp-cli/wp-cli"}, "time": "2025-05-07T01:16:12+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}