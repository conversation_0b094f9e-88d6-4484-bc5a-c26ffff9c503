<?php
class TCP_Parser {
    private $channel_name;
    private $proxy;
    private $timeout;

    public function __construct($channel_name, $proxy = '', $timeout = 30) {
        $this->channel_name = str_replace('@', '', $channel_name);
        $this->proxy = $proxy;
        $this->timeout = $timeout;
    }

    public function get_last_posts($limit = 10) {
        $url = "https://t.me/s/{$this->channel_name}";
        $html = $this->fetch_html($url);

        if (!$html) {
            return new WP_Error('tcp_fetch_error', 'Не удалось загрузить канал');
        }

        return $this->parse_html($html, $limit);
    }

    private function fetch_html($url) {
        $args = [
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'timeout' => $this->timeout,
        ];

        if (!empty($this->proxy)) {
            $args['proxy'] = $this->proxy;
        }

        $response = wp_remote_get($url, $args);
        
        if (is_wp_error($response)) {
            error_log('TCP Error: ' . $response->get_error_message());
            return false;
        }

        return wp_remote_retrieve_body($response);
    }

    private static function parse_html($html, $limit) {
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
    
        $xpath = new DOMXPath($dom);
        $posts = [];
        
        $message_nodes = $xpath->query("//div[contains(@class, 'tgme_widget_message') and @data-post]");
    
        foreach ($message_nodes as $node) {
            if (count($posts) >= $limit) break;

            // Ensure $node is a DOMElement before calling getAttribute
            if (!($node instanceof DOMElement)) {
                continue;
            }

            $post_id = $node->getAttribute('data-post');
            $post_id = explode('/', $post_id)[1] ?? $post_id;
    
            $text = '';
            $text_node = $xpath->query(".//div[contains(@class, 'tgme_widget_message_text')]", $node);
            if ($text_node->length > 0) {
                $text = trim($text_node->item(0)->textContent);
                $text = preg_replace('/\s+/', ' ', $text);
            }
    
            $media = [
                'images' => [],
                'videos' => []
            ];
    
            // Обработка обычных изображений
            $image_wraps = $xpath->query(".//a[contains(@class, 'tgme_widget_message_photo_wrap')]", $node);
            foreach ($image_wraps as $wrap) {
                if (!($wrap instanceof DOMElement)) {
                    continue;
                }
                $style = $wrap->getAttribute('style');
                if (preg_match('/background-image:url\(\'(.*?)\'\)/', $style, $matches)) {
                    $media['images'][] = $matches[1];
                }
            }
    
            // Обработка одиночных видео
            $video_thumbs = $xpath->query(".//i[contains(@class, 'tgme_widget_message_video_thumb')]", $node);
            $video_sources = $xpath->query(".//video[@src]", $node);
            
            for ($i = 0; $i < $video_thumbs->length; $i++) {
                $thumb_element = $video_thumbs->item($i);
                if (!($thumb_element instanceof DOMElement)) {
                    continue;
                }
                $thumb_style = $thumb_element->getAttribute('style');
                preg_match('/background-image:url\(\'(.*?)\'\)/', $thumb_style, $thumb_matches);

                $video_src = '';
                if ($i < $video_sources->length) {
                    $video_element = $video_sources->item($i);
                    if ($video_element instanceof DOMElement) {
                        $video_src = $video_element->getAttribute('src');
                    }
                }
                
                $media['videos'][] = [
                    'thumb' => $thumb_matches[1] ?? '',
                    'src' => $video_src
                ];
            }
    
            // Обработка групповых медиа
            $grouped_media = $xpath->query(".//div[contains(@class, 'tgme_widget_message_grouped_wrap')]", $node);
            if ($grouped_media->length > 0) {
                $group_videos = $xpath->query(".//a[contains(@class, 'tgme_widget_message_video_player')]", $grouped_media->item(0));
                
                foreach ($group_videos as $video) {
                    $thumb = $xpath->query(".//i[contains(@class, 'tgme_widget_message_video_thumb')]", $video);
                    $video_src = $xpath->query(".//video[@src]", $video);
                    
                    if ($thumb->length > 0) {
                        $thumb_element = $thumb->item(0);
                        if ($thumb_element instanceof DOMElement) {
                            $thumb_style = $thumb_element->getAttribute('style');
                            preg_match('/background-image:url\(\'(.*?)\'\)/', $thumb_style, $thumb_matches);

                            $video_src_url = '';
                            if ($video_src->length > 0) {
                                $video_src_element = $video_src->item(0);
                                if ($video_src_element instanceof DOMElement) {
                                    $video_src_url = $video_src_element->getAttribute('src');
                                }
                            }

                            $media['videos'][] = [
                                'thumb' => $thumb_matches[1] ?? '',
                                'src' => $video_src_url
                            ];
                        }
                    }
                }
            }
    
            $posts[] = [
                'id' => $post_id,
                'text' => $text,
                'media' => $media
            ];
        }
    
        return $posts;
    }
}