<?php

if (!class_exists('WP_CLI')) return;

class TCP_CLI_Command extends WP_CLI_Command {
    /**
     * Парсит Telegram-канал и сохраняет посты
     *
     * ## OPTIONS
     * <channel>
     * : Название канала (например, @durov)
     *
     * [--limit=<limit>]
     * : Ли<PERSON>ит постов (по умолчанию: 10)
     *
     * [--proxy=<proxy>]
     * : Прокси (опционально)
     *
     * [--delay=<delay>]
     * : Задержка между запросами в секундах (по умолчанию: 1)
     */
    public function parse($args, $assoc_args) {
        list($channel) = $args;
        $limit = $assoc_args['limit'] ?? 10;
        $proxy = $assoc_args['proxy'] ?? '';
        $delay = $assoc_args['delay'] ?? 1;

        WP_CLI::line("Парсинг канала {$channel}...");

        $parser = new TCP_Parser($channel, $proxy);
        $posts = $parser->get_last_posts($limit);

        if (is_wp_error($posts)) {
            WP_CLI::error($posts->get_error_message());
            return;
        }

        $saved_ids = [];

        $progress = WP_CLI\Utils\make_progress_bar('Обработка', count($posts));

        foreach ($posts as $post) {
            $post['channel'] = $channel;
            $post_id = TCP_Database::save_post($post);

            if ($post_id && !in_array($post_id, $saved_ids)) {
                $saved_ids[] = $post_id;
                WP_CLI::debug("Сохранен пост #{$post_id}");
            }

            $progress->tick();
            sleep($delay); // Задержка между запросами
        }

        $progress->finish();
        WP_CLI::success("Готово! Постов сохранено: " . count($saved_ids));
    }
}

WP_CLI::add_command('telegram-parser', 'TCP_CLI_Command');



//wp telegram-parser parse @channel --limit=5