<?php
class TCP_Database {
    public static function activate() {
        // При активации плагина
    }

    public static function deactivate() {
        // При деактивации
    }

    public static function save_post($post_data) {
        print_r("\n");
        print_r("Processing post: " . $post_data['id'] . "\n");

        // Создаем контент поста с медиа
        $post_content = $post_data['text'];
        $media_content = self::build_media_content($post_data);
        if (!empty($media_content)) {
            $post_content .= "\n\n" . $media_content;
        }

        // Создаем новый пост
        $post_id = wp_insert_post([
            'post_title' => wp_trim_words($post_data['text'], 10, '...'),
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'post',
        ]);

        if (is_wp_error($post_id) || !$post_id) {
            error_log('TCP Error: Failed to create post');
            return false;
        }

        // Сохраняем метаданные
        update_post_meta($post_id, 'tcp_message_id', $post_data['id']);
        update_post_meta($post_id, 'tcp_channel', $post_data['channel']);

        // Обрабатываем медиа файлы
        self::process_media($post_data, $post_id);

        return $post_id;
    }

    private static function build_media_content($post_data) {
        $content = '';

        if (empty($post_data['media'])) {
            return $content;
        }

        $media = $post_data['media'];

        // Добавляем изображения
        if (!empty($media['images'])) {
            foreach ($media['images'] as $image_url) {
                $content .= '<img src="' . esc_url($image_url) . '" alt="Telegram Image" style="max-width: 100%; height: auto;" />' . "\n";
            }
        }

        // Добавляем видео
        if (!empty($media['videos'])) {
            foreach ($media['videos'] as $video) {
                if (!empty($video['src'])) {
                    $poster = !empty($video['thumb']) ? ' poster="' . esc_url($video['thumb']) . '"' : '';
                    $content .= '<video controls style="max-width: 100%; height: auto;"' . $poster . '>' . "\n";
                    $content .= '  <source src="' . esc_url($video['src']) . '" type="video/mp4">' . "\n";
                    $content .= '  Ваш браузер не поддерживает видео.' . "\n";
                    $content .= '</video>' . "\n";
                } elseif (!empty($video['thumb'])) {
                    // Если нет видео, но есть превью, показываем как изображение
                    $content .= '<img src="' . esc_url($video['thumb']) . '" alt="Video Thumbnail" style="max-width: 100%; height: auto;" />' . "\n";
                }
            }
        }

        return $content;
    }

    private static function process_media($post_data, $post_id) {
        if (empty($post_data['media'])) {
            return;
        }

        $media = $post_data['media'];
        $featured_image_set = false;
        $uploaded_media_ids = [];

        // Обрабатываем изображения
        if (!empty($media['images'])) {
            foreach ($media['images'] as $image_url) {
                print_r('Uploading image: ' . $image_url . "\n");
                $media_id = self::upload_media($image_url, $post_id);

                if ($media_id) {
                    $uploaded_media_ids[] = $media_id;

                    // Устанавливаем первое изображение как миниатюру поста
                    if (!$featured_image_set) {
                        set_post_thumbnail($post_id, $media_id);
                        $featured_image_set = true;
                    }
                }
            }
        }

        // Обрабатываем видео
        if (!empty($media['videos'])) {
            foreach ($media['videos'] as $video) {
                // Загружаем видео файл, если есть
                if (!empty($video['src'])) {
                    print_r('Uploading video: ' . $video['src'] . "\n");
                    $video_id = self::upload_media($video['src'], $post_id);
                    if ($video_id) {
                        $uploaded_media_ids[] = $video_id;
                    }
                }

                // Загружаем превью видео
                if (!empty($video['thumb'])) {
                    print_r('Uploading video thumbnail: ' . $video['thumb'] . "\n");
                    $thumb_id = self::upload_media($video['thumb'], $post_id);

                    if ($thumb_id) {
                        $uploaded_media_ids[] = $thumb_id;

                        // Устанавливаем превью видео как миниатюру поста, если еще не установлена
                        if (!$featured_image_set) {
                            set_post_thumbnail($post_id, $thumb_id);
                            $featured_image_set = true;
                        }
                    }
                }
            }
        }

        // Обновляем контент поста с загруженными медиа
        if (!empty($uploaded_media_ids)) {
            self::update_post_content_with_media($post_id, $uploaded_media_ids);
        }
    }

    private static function upload_media($image_url, $post_id) {
        // Получаем информацию о папке uploads
        $upload_dir = wp_upload_dir();

        // print_r('Upload directory info:' . "\n");
        // print_r('Path: ' . $upload_dir['path'] . "\n");
        // print_r('URL: ' . $upload_dir['url'] . "\n");
        // print_r('Base dir: ' . $upload_dir['basedir'] . "\n");

        // Создаем папку, если она не существует
        if (!file_exists($upload_dir['path'])) {
            wp_mkdir_p($upload_dir['path']);
            print_r('Created directory: ' . $upload_dir['path'] . "\n");
        }

        // Проверяем права на запись
        if (!wp_is_writable($upload_dir['path'])) {
            // Пытаемся установить права
            chmod($upload_dir['path'], 0775);
            if (!wp_is_writable($upload_dir['path'])) {
                error_log('TCP Error: Upload directory is not writable: ' . $upload_dir['path']);
                print_r('Upload directory is not writable: ' . $upload_dir['path'] . "\n");
                return false;
            }
        }

        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');

        print_r('Attempting to download image from: ' . $image_url . "\n");
        $media_id = media_sideload_image($image_url, $post_id, null, 'id');

        if (!is_wp_error($media_id)) {
            set_post_thumbnail($post_id, $media_id);
            print_r('Image uploaded and set as thumbnail (ID: ' . $media_id . ')' . "\n");
            return $media_id;
        } else {
            print_r('Failed to upload image: ' . $media_id->get_error_message() . "\n");
            error_log('TCP Media Error: ' . $media_id->get_error_message());

            // Пробуем альтернативный метод загрузки
            print_r('Trying alternative upload method...' . "\n");
            return self::alternative_upload_media($image_url, $post_id);
        }
    }

    private static function alternative_upload_media($image_url, $post_id) {
        $upload_dir = wp_upload_dir();

        // Получаем содержимое изображения
        $image_data = wp_remote_get($image_url);
        if (is_wp_error($image_data)) {
            print_r('Failed to download image: ' . $image_data->get_error_message() . "\n");
            return false;
        }

        $image_content = wp_remote_retrieve_body($image_data);
        if (empty($image_content)) {
            print_r('Empty image content' . "\n");
            return false;
        }

        // Определяем расширение файла
        $extension = '';
        $mime_type = '';

        // Пытаемся определить тип файла по содержимому
        $image_info = @getimagesizefromstring($image_content);
        if ($image_info !== false) {
            // Это изображение
            $mime_type = $image_info['mime'];
            switch ($mime_type) {
                case 'image/jpeg':
                    $extension = '.jpg';
                    break;
                case 'image/png':
                    $extension = '.png';
                    break;
                case 'image/gif':
                    $extension = '.gif';
                    break;
                case 'image/webp':
                    $extension = '.webp';
                    break;
                default:
                    $extension = '.jpg';
                    $mime_type = 'image/jpeg';
            }
        } else {
            // Пытаемся определить по URL или заголовкам
            $url_extension = strtolower(pathinfo(parse_url($image_url, PHP_URL_PATH), PATHINFO_EXTENSION));
            switch ($url_extension) {
                case 'mp4':
                    $extension = '.mp4';
                    $mime_type = 'video/mp4';
                    break;
                case 'webm':
                    $extension = '.webm';
                    $mime_type = 'video/webm';
                    break;
                case 'mov':
                    $extension = '.mov';
                    $mime_type = 'video/quicktime';
                    break;
                default:
                    // По умолчанию считаем изображением
                    $extension = '.jpg';
                    $mime_type = 'image/jpeg';
            }
        }

        // Создаем уникальное имя файла
        $filename = 'telegram_' . time() . '_' . $post_id . $extension;
        $file_path = $upload_dir['path'] . '/' . $filename;

        // Сохраняем файл
        $saved = file_put_contents($file_path, $image_content);
        if ($saved === false) {
            print_r('Failed to save image file: ' . $file_path . "\n");
            return false;
        }

        // Создаем запись в медиабиблиотеке
        $attachment = array(
            'guid' => $upload_dir['url'] . '/' . $filename,
            'post_mime_type' => $mime_type,
            'post_title' => sanitize_file_name($filename),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attach_id = wp_insert_attachment($attachment, $file_path, $post_id);
        if (is_wp_error($attach_id)) {
            print_r('Failed to create attachment: ' . $attach_id->get_error_message() . "\n");
            return false;
        }

        // Генерируем метаданные
        require_once(ABSPATH . 'wp-admin/includes/image.php');

        if (strpos($mime_type, 'image/') === 0) {
            // Для изображений генерируем стандартные метаданные
            $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
            wp_update_attachment_metadata($attach_id, $attach_data);
            print_r('Image uploaded via alternative method (ID: ' . $attach_id . ')' . "\n");
        } else {
            // Для видео создаем базовые метаданные
            $attach_data = array(
                'file' => $filename,
                'filesize' => filesize($file_path)
            );
            wp_update_attachment_metadata($attach_id, $attach_data);
            print_r('Video uploaded via alternative method (ID: ' . $attach_id . ')' . "\n");
        }
        return $attach_id;
    }

    /**
     * Обновляет контент поста, встраивая загруженные медиа файлы
     */
    private static function update_post_content_with_media($post_id, $media_ids) {
        if (empty($media_ids)) {
            return;
        }

        $post = get_post($post_id);
        if (!$post) {
            return;
        }

        $content = $post->post_content;
        $media_html = '';

        foreach ($media_ids as $media_id) {
            $attachment = get_post($media_id);
            if (!$attachment) {
                continue;
            }

            $mime_type = get_post_mime_type($media_id);

            if (strpos($mime_type, 'image/') === 0) {
                // Для изображений используем wp_get_attachment_image
                $image_html = wp_get_attachment_image($media_id, 'large', false, [
                    'style' => 'max-width: 100%; height: auto;'
                ]);
                $media_html .= $image_html . "\n";
            } elseif (strpos($mime_type, 'video/') === 0) {
                // Для видео создаем HTML5 video элемент
                $video_url = wp_get_attachment_url($media_id);
                $media_html .= '<video controls style="max-width: 100%; height: auto;">' . "\n";
                $media_html .= '  <source src="' . esc_url($video_url) . '" type="' . esc_attr($mime_type) . '">' . "\n";
                $media_html .= '  Ваш браузер не поддерживает видео.' . "\n";
                $media_html .= '</video>' . "\n";
            }
        }

        if (!empty($media_html)) {
            // Добавляем медиа в конец контента
            $updated_content = $content . "\n\n" . $media_html;

            wp_update_post([
                'ID' => $post_id,
                'post_content' => $updated_content
            ]);
        }
    }
}