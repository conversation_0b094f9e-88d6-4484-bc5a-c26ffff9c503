<?php
class TCP_Database {
    public static function activate() {
        // При активации плагина
    }

    public static function deactivate() {
        // При деактивации
    }

    public static function save_post($post_data) { 

        print_r("\n");
        //print_r($post_data['text'] . "\n");
        print_r($post_data['image'] . "\n");

        // Создаем новый пост
        $post_id = wp_insert_post([
            'post_title' => wp_trim_words($post_data['text'], 10, '...'),
            'post_content' => $post_data['text'],
            'post_status' => 'publish',
            'post_type' => 'post',
        ]);

        if (is_wp_error($post_id) || !$post_id) {
            error_log('TCP Error: Failed to create post');
            return false;
        }

        // Сохраняем метаданные
        update_post_meta($post_id, 'tcp_message_id', $post_data['id']);
        update_post_meta($post_id, 'tcp_channel', $post_data['channel']);

        // Загружаем изображение и устанавоиваем его как миниатюру
        if (!empty($post_data['image'])) {
            print_r('Uploading image: ' . $post_data['image'] . "\n");
            self::upload_media($post_data['image'], $post_id);
        }

        return $post_id;
    }

    private static function upload_media($image_url, $post_id) {
        // Получаем информацию о папке uploads
        $upload_dir = wp_upload_dir();

        // print_r('Upload directory info:' . "\n");
        // print_r('Path: ' . $upload_dir['path'] . "\n");
        // print_r('URL: ' . $upload_dir['url'] . "\n");
        // print_r('Base dir: ' . $upload_dir['basedir'] . "\n");

        // Создаем папку, если она не существует
        if (!file_exists($upload_dir['path'])) {
            wp_mkdir_p($upload_dir['path']);
            print_r('Created directory: ' . $upload_dir['path'] . "\n");
        }

        // Проверяем права на запись
        if (!wp_is_writable($upload_dir['path'])) {
            // Пытаемся установить права
            chmod($upload_dir['path'], 0775);
            if (!wp_is_writable($upload_dir['path'])) {
                error_log('TCP Error: Upload directory is not writable: ' . $upload_dir['path']);
                print_r('Upload directory is not writable: ' . $upload_dir['path'] . "\n");
                return false;
            }
        }

        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');

        print_r('Attempting to download image from: ' . $image_url . "\n");
        $media_id = media_sideload_image($image_url, $post_id, null, 'id');

        if (!is_wp_error($media_id)) {
            set_post_thumbnail($post_id, $media_id);
            print_r('Image uploaded and set as thumbnail (ID: ' . $media_id . ')' . "\n");
            return $media_id;
        } else {
            print_r('Failed to upload image: ' . $media_id->get_error_message() . "\n");
            error_log('TCP Media Error: ' . $media_id->get_error_message());

            // Пробуем альтернативный метод загрузки
            print_r('Trying alternative upload method...' . "\n");
            return self::alternative_upload_media($image_url, $post_id);
        }
    }

    private static function alternative_upload_media($image_url, $post_id) {
        $upload_dir = wp_upload_dir();

        // Получаем содержимое изображения
        $image_data = wp_remote_get($image_url);
        if (is_wp_error($image_data)) {
            print_r('Failed to download image: ' . $image_data->get_error_message() . "\n");
            return false;
        }

        $image_content = wp_remote_retrieve_body($image_data);
        if (empty($image_content)) {
            print_r('Empty image content' . "\n");
            return false;
        }

        // Определяем расширение файла
        $image_info = getimagesizefromstring($image_content);
        $extension = '';
        switch ($image_info['mime']) {
            case 'image/jpeg':
                $extension = '.jpg';
                break;
            case 'image/png':
                $extension = '.png';
                break;
            case 'image/gif':
                $extension = '.gif';
                break;
            case 'image/webp':
                $extension = '.webp';
                break;
            default:
                $extension = '.jpg';
        }

        // Создаем уникальное имя файла
        $filename = 'telegram_' . time() . '_' . $post_id . $extension;
        $file_path = $upload_dir['path'] . '/' . $filename;

        // Сохраняем файл
        $saved = file_put_contents($file_path, $image_content);
        if ($saved === false) {
            print_r('Failed to save image file: ' . $file_path . "\n");
            return false;
        }

        // Создаем запись в медиабиблиотеке
        $attachment = array(
            'guid' => $upload_dir['url'] . '/' . $filename,
            'post_mime_type' => $image_info['mime'],
            'post_title' => sanitize_file_name($filename),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attach_id = wp_insert_attachment($attachment, $file_path, $post_id);
        if (is_wp_error($attach_id)) {
            print_r('Failed to create attachment: ' . $attach_id->get_error_message() . "\n");
            return false;
        }

        // Генерируем метаданные для изображения
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
        wp_update_attachment_metadata($attach_id, $attach_data);

        // Устанавливаем как миниатюру поста
        set_post_thumbnail($post_id, $attach_id);

        print_r('Image uploaded via alternative method (ID: ' . $attach_id . ')' . "\n");
        return $attach_id;
    }
}