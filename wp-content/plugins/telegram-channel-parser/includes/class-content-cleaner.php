<?php
/**
 * Класс для очистки контента постов от нежелательных элементов
 */
class TCP_Content_Cleaner {
    
    /**
     * Удаляет из текста все слова, начинающиеся с @
     * 
     * @param string $content Исходный контент
     * @return string Очищенный контент
     */
    public static function remove_mentions($content) {
        if (empty($content)) {
            return $content;
        }
        
        // Удаляем слова, начинающиеся с @ (упоминания каналов/пользователей)
        // Паттерн ищет @ + любые символы до пробела, знака препинания или конца строки
        $pattern = '/@[^\s\.,;:!?\)\]\}]+/u';
        $cleaned_content = preg_replace($pattern, '', $content);
        
        // Убираем лишние пробелы, которые могли остаться после удаления упоминаний
        $cleaned_content = preg_replace('/\s+/', ' ', $cleaned_content);
        
        // Убираем пробелы в начале и конце
        $cleaned_content = trim($cleaned_content);
        
        return $cleaned_content;
    }
    
    /**
     * Удаляет из текста хештеги (#hashtag)
     * 
     * @param string $content Исходный контент
     * @return string Очищенный контент
     */
    public static function remove_hashtags($content) {
        if (empty($content)) {
            return $content;
        }
        
        // Удаляем хештеги
        $pattern = '/#[^\s\.,;:!?\)\]\}]+/u';
        $cleaned_content = preg_replace($pattern, '', $content);
        
        // Убираем лишние пробелы
        $cleaned_content = preg_replace('/\s+/', ' ', $cleaned_content);
        $cleaned_content = trim($cleaned_content);
        
        return $cleaned_content;
    }
    
    /**
     * Удаляет из текста ссылки (http/https)
     * 
     * @param string $content Исходный контент
     * @return string Очищенный контент
     */
    public static function remove_links($content) {
        if (empty($content)) {
            return $content;
        }
        
        // Удаляем HTTP/HTTPS ссылки
        $pattern = '/https?:\/\/[^\s\.,;:!?\)\]\}]+/i';
        $cleaned_content = preg_replace($pattern, '', $content);
        
        // Убираем лишние пробелы
        $cleaned_content = preg_replace('/\s+/', ' ', $cleaned_content);
        $cleaned_content = trim($cleaned_content);
        
        return $cleaned_content;
    }
    
    /**
     * Комплексная очистка контента
     * Удаляет упоминания, хештеги и ссылки
     * 
     * @param string $content Исходный контент
     * @param array $options Опции очистки ['mentions' => true, 'hashtags' => false, 'links' => false]
     * @return string Очищенный контент
     */
    public static function clean_content($content, $options = []) {
        if (empty($content)) {
            return $content;
        }
        
        // Настройки по умолчанию
        $default_options = [
            'mentions' => true,   // Удалять упоминания @username
            'hashtags' => false,  // Удалять хештеги #hashtag
            'links' => false      // Удалять ссылки http://...
        ];
        
        $options = array_merge($default_options, $options);
        
        $cleaned_content = $content;
        
        // Удаляем упоминания
        if ($options['mentions']) {
            $cleaned_content = self::remove_mentions($cleaned_content);
        }
        
        // Удаляем хештеги
        if ($options['hashtags']) {
            $cleaned_content = self::remove_hashtags($cleaned_content);
        }
        
        // Удаляем ссылки
        if ($options['links']) {
            $cleaned_content = self::remove_links($cleaned_content);
        }
        
        return $cleaned_content;
    }
    
    /**
     * Очищает заголовок поста
     * Удаляет упоминания и ограничивает длину
     * 
     * @param string $title Исходный заголовок
     * @param int $max_length Максимальная длина заголовка
     * @return string Очищенный заголовок
     */
    public static function clean_title($title, $max_length = 100) {
        if (empty($title)) {
            return $title;
        }
        
        // Удаляем упоминания из заголовка
        $cleaned_title = self::remove_mentions($title);
        
        // Ограничиваем длину заголовка
        if (mb_strlen($cleaned_title) > $max_length) {
            $cleaned_title = mb_substr($cleaned_title, 0, $max_length - 3) . '...';
        }
        
        return $cleaned_title;
    }
}
