<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mustache/mustache' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '^2.14.2',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'ec2344cf77a48253bbca6939aa3d2477773ea63d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/mustache' => array(
            'pretty_version' => 'v2.14.99',
            'version' => '2.14.99.0',
            'reference' => 'ca23b97ac35fbe01c160549eb634396183d04a59',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/mustache',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/mustangostang-spyc' => array(
            'pretty_version' => '0.6.3',
            'version' => '0.6.3.0',
            'reference' => '6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/mustangostang-spyc',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/php-cli-tools' => array(
            'pretty_version' => 'v0.12.5',
            'version' => '0.12.5.0',
            'reference' => '34b83b4f700df8a4ec3fd17bf7e7e7d8ca5f28da',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/php-cli-tools',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/wp-cli' => array(
            'pretty_version' => 'v2.12.0',
            'version' => '2.12.0.0',
            'reference' => '03d30d4138d12b4bffd8b507b82e56e129e0523f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/wp-cli',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
    ),
);
