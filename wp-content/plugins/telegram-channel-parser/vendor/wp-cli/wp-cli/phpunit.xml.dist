<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/4.8/phpunit.xsd"
		 bootstrap="tests/bootstrap.php"
		 backupGlobals="false"
		 beStrictAboutCoversAnnotation="true"
		 beStrictAboutOutputDuringTests="true"
		 beStrictAboutTestsThatDoNotTestAnything="true"
		 beStrictAboutTodoAnnotatedTests="true"
		 convertErrorsToExceptions="true"
		 convertWarningsToExceptions="true"
		 convertNoticesToExceptions="true"
		 convertDeprecationsToExceptions="true"
		 colors="true"
		 verbose="true">
	<testsuites>
		<testsuite name="wp-cli/wp-cli tests">
			<directory suffix="Test.php">tests</directory>
		</testsuite>
	</testsuites>

	<coverage processUncoveredFiles="false">
		<include>
			<directory suffix=".php">php</directory>
		</include>
	</coverage>
</phpunit>
