---
name: "\U0001F41B Bug Report"
about: "Something isn't working as expected"
title: ''
labels: 'i: bug'
assignees: ''

---

## Bug Report

<!--- ⚠️ Before you start reporting the bug... ---

**Is your bug report directly related to a specific command?**

If yes, make sure you create the issue in the correct repository.

Here's a quick overview to where each command lives:

* `wp (cache|transient) *` https://github.com/wp-cli/cache-command
* `wp checksum` https://github.com/wp-cli/checksum-command
* `wp config *` https://github.com/wp-cli/config-command
* `wp core *` https://github.com/wp-cli/core-command
* `wp cron *` https://github.com/wp-cli/cron-command
* `wp db *` https://github.com/wp-cli/db-command
* `wp embed *` https://github.com/wp-cli/embed-command
* `wp (eval|eval-file)` https://github.com/wp-cli/eval-command
* `wp export` https://github.com/wp-cli/export-command
* `wp (option|post|comment|user|term|site) *` https://github.com/wp-cli/entity-command
* `wp i18n` https://github.com/wp-cli/i18n-command
* `wp import` https://github.com/wp-cli/import-command
* `wp language` https://github.com/wp-cli/language-command
* `wp maintenance-mode *` https://github.com/wp-cli/maintenance-mode-command
* `wp media *` https://github.com/wp-cli/media-command
* `wp package *` https://github.com/wp-cli/package-command
* `wp (plugin|theme) *` https://github.com/wp-cli/extension-command
* `wp rewrite` https://github.com/wp-cli/rewrite-command
* `wp (role|cap) *` https://github.com/wp-cli/role-command
* `wp scaffold *` https://github.com/wp-cli/scaffold-command
* `wp search-replace` https://github.com/wp-cli/search-replace-command
* `wp server` https://github.com/wp-cli/server-command
* `wp shell` https://github.com/wp-cli/shell-command
* `wp super-admin *` https://github.com/wp-cli/super-admin-command
* `wp (widget|sidebar) *` https://github.com/wp-cli/widget-command

If you are not in the correct repository, you can just close this issue/window without submitting and navigate to the correct one.

**Are you unsure about which repository to post the bug report into?**

Just head over to the [`wp-cli/wp-cli`](https://github.com/wp-cli/wp-cli) repository and [create a new issue in that repository](https://github.com/wp-cli/wp-cli/issues/new). The maintainers can still move the bug report into the correct repository later on.

--- ✅ If you are in the correct location now... --->

- [ ] Yes, I reviewed the [contribution guidelines](https://make.wordpress.org/cli/handbook/contributing/).
- [ ] Yes, more specifically, I reviewed the guidelines on [how to write clear bug reports](https://make.wordpress.org/cli/handbook/bug-reports/).

**Describe the current, buggy behavior**

A clear and concise description of the behavior that produces an incorrect result or error.

Remember: more information is better. Please provide context on how you're running the command to make sure we're all on the same page when reasoning about this.

**Describe how other contributors can replicate this bug**

- a list of
- steps to replicate
- the error condition

```js
// You can also use code snippets if needed.
```

**Describe what you expect as the correct outcome**

A clear and concise description of what you expected to happen (or code).

**Let us know what environment you are running this on**

```
(Paste the output of "wp cli info" into this box)
```

**Provide a possible solution**

If you happen to have a suggestion on how to fix this bug, please tell us in here.

Just leave this section out if you don't know how to fix it.

**Provide additional context/screenshots**

Add any other context about the problem here.

If applicable, add screenshots to help explain (you can just drag&drop images into the GitHub issue).
