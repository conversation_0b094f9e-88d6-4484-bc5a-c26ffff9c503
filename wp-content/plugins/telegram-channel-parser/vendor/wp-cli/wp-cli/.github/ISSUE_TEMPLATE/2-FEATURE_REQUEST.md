---
name: "\U0001F195 Feature Request"
about: "I have a suggestion for missing functionality or improvements"
title: ''
labels: 'i: enhancement'
assignees: ''

---

## Feature Request

<!--- ⚠️ Before you describe your requested feature... ---

**Is your feature request directly related to a specific, existing command?**

If yes, make sure you are in the correct repository that contains the command you are referring to, and only create the issue in that repository.

Here's a quick overview of where to find the different commands:

* `wp (cache|transient) *` https://github.com/wp-cli/cache-command
* `wp checksum` https://github.com/wp-cli/checksum-command
* `wp config *` https://github.com/wp-cli/config-command
* `wp core *` https://github.com/wp-cli/core-command
* `wp cron *` https://github.com/wp-cli/cron-command
* `wp db *` https://github.com/wp-cli/db-command
* `wp embed *` https://github.com/wp-cli/embed-command
* `wp (eval|eval-file)` https://github.com/wp-cli/eval-command
* `wp export` https://github.com/wp-cli/export-command
* `wp (option|post|comment|user|term|site) *` https://github.com/wp-cli/entity-command
* `wp i18n` https://github.com/wp-cli/i18n-command
* `wp import` https://github.com/wp-cli/import-command
* `wp language` https://github.com/wp-cli/language-command
* `wp maintenance-mode *` https://github.com/wp-cli/maintenance-mode-command
* `wp media *` https://github.com/wp-cli/media-command
* `wp package *` https://github.com/wp-cli/package-command
* `wp (plugin|theme) *` https://github.com/wp-cli/extension-command
* `wp rewrite` https://github.com/wp-cli/rewrite-command
* `wp (role|cap) *` https://github.com/wp-cli/role-command
* `wp scaffold *` https://github.com/wp-cli/scaffold-command
* `wp search-replace` https://github.com/wp-cli/search-replace-command
* `wp server` https://github.com/wp-cli/server-command
* `wp shell` https://github.com/wp-cli/shell-command
* `wp super-admin *` https://github.com/wp-cli/super-admin-command
* `wp (widget|sidebar) *` https://github.com/wp-cli/widget-command

If you are not in the correct repository right now, you can just close this issue/window without submitting and click through to the correct one.

**Is your feature request about a new command, or a more general idea?**

If your feature request is not about making a change to one or more existing commands, but rather about adding functionality that doesn't naturally fit one of the existing commands, head over to the [`wp-cli/ideas`](https://github.com/wp-cli/ideas) repository and [create a new issue in that repository](https://github.com/wp-cli/ideas/issues/new).

The issue tracker in that repository will collect ideas that are still vague and need fleshing out and/or preparatory work (like creating a new repository) before the actual development work can begin.

--- ✅ If you are in the correct location now... ---> 

- [ ] Yes, I reviewed the [contribution guidelines](https://make.wordpress.org/cli/handbook/contributing/).

**Describe your use case and the problem you are facing**

A clear and concise description of what you are actually trying to do, and in what way the current version of WP-CLI is not up to the task.

**Describe the solution you'd like**

A clear and concise description of what you want to happen. Add any considered drawbacks if you can think of any.
