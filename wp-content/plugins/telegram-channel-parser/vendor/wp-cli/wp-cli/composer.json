{"name": "wp-cli/wp-cli", "description": "WP-CLI framework", "keywords": ["cli", "wordpress"], "homepage": "https://wp-cli.org", "license": "MIT", "require": {"php": "^5.6 || ^7.0 || ^8.0", "ext-curl": "*", "symfony/finder": ">2.7", "wp-cli/mustache": "^2.14.99", "wp-cli/mustangostang-spyc": "^0.6.3", "wp-cli/php-cli-tools": "~0.12.4"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.2 || ^2", "wp-cli/extension-command": "^1.1 || ^2", "wp-cli/package-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^4.3.10"}, "suggest": {"ext-readline": "Include for a better --prompt implementation", "ext-zip": "Needed to support extraction of ZIP archives when doing downloads or updates"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "johnpbloch/wordpress-core-installer": true}, "process-timeout": 7200, "sort-packages": true, "lock": false}, "extra": {"branch-alias": {"dev-main": "2.12.x-dev"}}, "autoload": {"psr-0": {"WP_CLI\\": "php/"}, "classmap": ["php/class-wp-cli.php", "php/class-wp-cli-command.php"]}, "minimum-stability": "dev", "prefer-stable": true, "bin": ["bin/wp", "bin/wp.bat"], "scripts": {"behat": "run-behat-tests", "behat-rerun": "rerun-behat-tests", "lint": "run-linter-tests", "phpcs": "run-phpcs-tests", "phpcbf": "run-phpcbf-cleanup", "phpunit": "run-php-unit-tests", "prepare-tests": "install-package-tests", "test": ["@lint", "@phpcs", "@phpunit", "@behat"]}, "support": {"issues": "https://github.com/wp-cli/wp-cli/issues", "source": "https://github.com/wp-cli/wp-cli", "docs": "https://make.wordpress.org/cli/handbook/"}}