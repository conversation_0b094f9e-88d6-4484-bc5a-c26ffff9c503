name: Tests

on:
  push:
  pull_request:
  schedule:
    - cron: '0 0 * * *'

jobs:
  tests:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        php:
          - 5.6
          - 7.0
          - 7.1
          - 7.2
          - 7.3
          - 7.4
          - 8.0
          - 8.1
          - 8.2
          - 8.3
          - 8.4

    name: PHP ${{ matrix.php }}

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Install PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          coverage: none

      - name: Install dependencies
        run: composer install --prefer-dist --no-interaction --no-progress

      - name: Run tests
        run: vendor/bin/phpunit
