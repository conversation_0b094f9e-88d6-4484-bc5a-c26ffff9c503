{"name": "wp-cli/mustache", "description": "A Mustache implementation in PHP.", "keywords": ["templating", "mustache"], "homepage": "https://github.com/bobthecow/mustache.php", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "require": {"php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.19.3", "yoast/phpunit-polyfills": "^2.0"}, "replace": {"mustache/mustache": "^2.14.2"}, "autoload": {"psr-0": {"Mustache": "src/"}}}