<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/4.8/phpunit.xsd"
		 bootstrap="tests/bootstrap.php"
		 backupGlobals="false"
		 beStrictAboutCoversAnnotation="true"
		 beStrictAboutOutputDuringTests="true"
		 beStrictAboutTestsThatDoNotTestAnything="true"
		 beStrictAboutTodoAnnotatedTests="true"
		 convertErrorsToExceptions="true"
		 convertWarningsToExceptions="true"
		 convertNoticesToExceptions="true"
		 convertDeprecationsToExceptions="true"
		 colors="true"
		 verbose="true">
	<testsuite name="wp-cli/php-cli-tools tests">
		<directory prefix="Test" suffix=".php">tests/</directory>
	</testsuite>

	<filter>
		<whitelist processUncoveredFilesFromWhitelist="true">
			<directory suffix=".php">lib/</directory>
		</whitelist>
	</filter>
</phpunit>
