<?php

require_once 'common.php';
/*
 * Please notice that the data has to be an 0-based array,
 * not an associative one if you intend to work with custom
 * column widths.
 */
$headers = array('First Name', 'Last Name', 'City', 'State');
$data = array(
	array('<PERSON><PERSON>',   '<PERSON>',    'Elizabeth City',   '<PERSON>'),
	array('<PERSON>',    '<PERSON>', '<PERSON><PERSON><PERSON>',         '<PERSON>'),
	array('All<PERSON><PERSON>',  '<PERSON>',    'Altoona',          'ME'),
	array('<PERSON>',   '<PERSON><PERSON>',      '<PERSON>',           '<PERSON>'),
	array('<PERSON>',     '<PERSON><PERSON><PERSON><PERSON>',  'San Francisco',    'ID'),
	array('<PERSON>',   'Tate',       'Chattanooga',      'FL'),
	array('<PERSON>',   '<PERSON>',    'Valdos<PERSON>',         'AB'),
	array('<PERSON>',   '<PERSON>',    '<PERSON><PERSON>',           'SK'),
	array('<PERSON><PERSON><PERSON>u',  '<PERSON><PERSON>',     '<PERSON><PERSON><PERSON><PERSON>',      'WI'),
	array('<PERSON>',    '<PERSON>',    'Garden Grove',     'KY'),
	array('<PERSON>',   '<PERSON>',      'La Habra',         'AZ'),
	array('<PERSON><PERSON>',  '<PERSON>',       '<PERSON>',           '<PERSON>'),
	array('<PERSON>',  '<PERSON><PERSON>',    'Statesboro',       '<PERSON>'),
	array('<PERSON>',  '<PERSON><PERSON><PERSON>',    'West Valley City', '<PERSON>'),
	array('Rhonda',   'Potter',     'Racine',           'BC'),
	array('<PERSON>',   '<PERSON><PERSON>zquez',  'Cedarburg',        'BC'),
	array('<PERSON>',  'Fletcher',   'Corpus Christi',   'BC'),
	array('Cheyenne', 'Rios',       'Broken Arrow',     'VA'),
	array('Velma',    'Clemons',    'Helena',           'IL'),
	array('Samuel',   '<PERSON>',      'Lawrenceville',    'NU'),
	array('Marcia',   'Swanson',    'Fontana',          'QC'),
	array('Zachary',  'Silva',      'Port Washington',  'MB'),
	array('Hilary',   'Chambers',   'Suffolk',          'HI'),
	array('Idola',    'Carroll',    'West Sacramento',  'QC'),
	array('Kirestin', 'Stephens',   'Fitchburg',        'AB'),
);

$table = new \cli\Table();
$table->setHeaders($headers);
$table->setRows($data);
$table->setRenderer(new \cli\table\Ascii([10, 10, 20, 5]));
$table->display();
