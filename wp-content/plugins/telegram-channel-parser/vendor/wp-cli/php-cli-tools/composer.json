{"name": "wp-cli/php-cli-tools", "type": "library", "description": "Console utilities for PHP", "keywords": ["console", "cli"], "homepage": "http://github.com/wp-cli/php-cli-tools", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">= 5.6.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "wp-cli/wp-cli-tests": "^4"}, "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-0": {"cli": "lib/"}, "files": ["lib/cli/cli.php"]}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "johnpbloch/wordpress-core-installer": true}}, "scripts": {"behat": "run-behat-tests", "behat-rerun": "rerun-behat-tests", "lint": "run-linter-tests", "phpcs": "run-phpcs-tests", "phpunit": "run-php-unit-tests", "prepare-tests": "install-package-tests", "test": ["@lint", "@phpcs", "@phpunit", "@behat"]}}