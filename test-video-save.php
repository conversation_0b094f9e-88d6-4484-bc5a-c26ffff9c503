<?php
/**
 * Тестовый скрипт для проверки сохранения видео в WordPress
 * Запускать из корня WordPress: php test-video-save.php
 */

// Подключаем WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Подключаем наш класс
require_once('wp-content/plugins/telegram-channel-parser/includes/class-database.php');

// Тестовые данные с видео
$test_post_data = [
    'id' => 'test_123',
    'text' => 'Тестовый пост с видео и изображениями',
    'channel' => 'test_channel',
    'media' => [
        'images' => [
            'https://via.placeholder.com/600x400/FF0000/FFFFFF?text=Test+Image+1',
            'https://via.placeholder.com/600x400/00FF00/FFFFFF?text=Test+Image+2'
        ],
        'videos' => [
            [
                'thumb' => 'https://via.placeholder.com/600x400/0000FF/FFFFFF?text=Video+Thumb+1',
                'src' => 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
            ],
            [
                'thumb' => 'https://via.placeholder.com/600x400/FFFF00/000000?text=Video+Thumb+2',
                'src' => ''  // Только превью
            ]
        ]
    ]
];

echo "Начинаем тест сохранения поста с видео...\n";

// Сохраняем пост
$post_id = TCP_Database::save_post($test_post_data);

if ($post_id) {
    echo "Пост успешно сохранен с ID: $post_id\n";
    
    // Получаем созданный пост
    $post = get_post($post_id);
    echo "Заголовок: " . $post->post_title . "\n";
    echo "Контент:\n" . $post->post_content . "\n";
    
    // Проверяем миниатюру
    $thumbnail_id = get_post_thumbnail_id($post_id);
    if ($thumbnail_id) {
        echo "Миниатюра установлена (ID: $thumbnail_id)\n";
    } else {
        echo "Миниатюра не установлена\n";
    }
    
    // Получаем все прикрепленные медиа
    $attachments = get_attached_media('', $post_id);
    echo "Прикреплено медиа файлов: " . count($attachments) . "\n";
    
    foreach ($attachments as $attachment) {
        echo "- " . $attachment->post_title . " (" . $attachment->post_mime_type . ")\n";
    }
    
} else {
    echo "Ошибка при сохранении поста\n";
}

echo "Тест завершен.\n";
